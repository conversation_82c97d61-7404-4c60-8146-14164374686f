<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Profile Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #000000;
            --secondary-color: #ffffff;
            --surface-color: #fafafa;
            --border-color: #e0e0e0;
            --text-primary: #212121;
            --text-secondary: #757575;
            --shadow-light: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --shadow-medium: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
            --border-radius: 8px;
            --transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            background-color: var(--surface-color);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 0.875rem;
            font-weight: 400;
            line-height: 1.5;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #333333 100%);
            color: var(--secondary-color);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: var(--shadow-medium);
            backdrop-filter: blur(10px);
        }

        .header h4 {
            font-weight: 600;
            margin: 0;
            font-size: 1.25rem;
        }

        .header .badge {
            background-color: rgba(255, 255, 255, 0.2);
            color: var(--secondary-color);
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .footer {
            background: linear-gradient(135deg, var(--primary-color) 0%, #333333 100%);
            color: var(--secondary-color);
            padding: 0.75rem 0;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .main-content {
            margin-top: 90px;
            margin-bottom: 70px;
            padding: 0;
            margin-right: 0;
            height: calc(100vh - 160px);
            overflow: hidden;
            width: 100vw;
            margin-left: 0;
            margin-right: 0;
        }

        .full-width-container {
            display: flex;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
        }

        .profile-column {
            width: 20%;
            padding: 0.5rem 0.5rem 0.5rem 0;
            height: calc(100vh - 160px);
            overflow-y: auto;
        }

        .data-column {
            width: 80%;
            padding: 0.5rem 0 0.5rem 0.5rem;
            height: calc(100vh - 160px);
            overflow-y: auto;
        }

        /* Compact scrollbar styling */
        .profile-column::-webkit-scrollbar,
        .data-column::-webkit-scrollbar {
            width: 6px;
        }

        .profile-column::-webkit-scrollbar-track,
        .data-column::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .profile-column::-webkit-scrollbar-thumb,
        .data-column::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .profile-column::-webkit-scrollbar-thumb:hover,
        .data-column::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .form-control, .form-select {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 0.75rem;
            padding: 0.4rem 0.6rem;
            height: 34px;
            background-color: var(--secondary-color);
            transition: var(--transition);
            font-weight: 400;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
            outline: none;
            background-color: var(--secondary-color);
        }

        .form-control:hover, .form-select:hover {
            border-color: #bdbdbd;
        }

        .form-floating > .form-control, .form-floating > .form-select {
            height: 40px;
            padding: 0.6rem 0.6rem 0.2rem;
            font-size: 0.75rem;
        }

        .form-floating > label {
            padding: 0.6rem 0.6rem;
            font-size: 0.65rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .profile-card {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            padding: 0.75rem;
            margin: 0;
            border: 1px solid var(--border-color);
            transition: var(--transition);
            height: 100%;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .profile-card:hover {
            box-shadow: var(--shadow-medium);
        }

        .section-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #333333 100%);
            color: var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            font-weight: 600;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            box-shadow: var(--shadow-light);
        }

        .section-header i {
            margin-right: 0.5rem;
            font-size: 1rem;
        }

        .grid-container {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            margin-bottom: 0;
            height: 160px;
            overflow: hidden;
            border: 1px solid var(--border-color);
            transition: var(--transition);
            display: flex;
            flex-direction: column;
        }

        .grid-container:hover {
            box-shadow: var(--shadow-medium);
        }

        .grid-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #333333 100%);
            color: var(--secondary-color);
            padding: 0.4rem 0.8rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 32px;
        }

        .grid-header-left {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .grid-header-right {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-add {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-size: 0.7rem;
            transition: var(--transition);
        }

        .btn-add:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .grid-header strong {
            font-weight: 600;
            color: var(--secondary-color);
            font-size: 0.8rem;
        }

        .grid-search {
            width: 140px;
            height: 26px;
            font-size: 0.7rem;
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.25rem 0.5rem;
            background: rgba(255, 255, 255, 0.9);
            color: var(--text-primary);
        }

        .grid-search:focus {
            background: var(--secondary-color);
            border-color: var(--border-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
        }

        .grid-content {
            flex: 1;
            overflow: hidden;
            min-height: 0;
            height: 96px;
        }

        .grid-content table {
            table-layout: fixed;
            width: 100%;
            height: 100%;
        }

        .grid-content th,
        .grid-content td {
            padding: 0.25rem 0.3rem;
            font-size: 0.65rem;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            height: 24px;
            line-height: 1.1;
            vertical-align: middle;
        }

        .grid-content th {
            background-color: #f8f9fa;
            font-weight: 600;
            border-bottom: 1px solid var(--border-color);
        }

        .grid-content th {
            background-color: var(--surface-color);
            font-weight: 600;
            border-bottom: 2px solid var(--border-color);
            height: 32px;
        }

        .grid-content tbody tr {
            height: 28px;
        }

        .grid-pagination {
            background: linear-gradient(135deg, var(--surface-color) 0%, #f5f5f5 100%);
            padding: 0.3rem 0.8rem;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.65rem;
            color: var(--text-secondary);
            height: 32px;
        }

        .pagination-input {
            width: 35px;
            height: 20px;
            font-size: 0.65rem;
            text-align: center;
            border: 1px solid var(--border-color);
            border-radius: 3px;
            margin: 0 0.2rem;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .table-sm {
            font-size: 0.75rem;
            margin-bottom: 0;
        }

        .table-sm th {
            padding: 0.5rem 0.5rem;
            vertical-align: middle;
            background-color: var(--surface-color);
            border-bottom: 2px solid var(--border-color);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.7rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table-sm td {
            padding: 0.5rem 0.5rem;
            vertical-align: middle;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.75rem;
        }

        .table-striped > tbody > tr:nth-of-type(odd) > td {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .btn-sm {
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            border-radius: 20px;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-outline-dark {
            border-color: var(--border-color);
            color: var(--text-secondary);
        }

        .btn-outline-dark:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--secondary-color);
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-active { background-color: #4caf50; }
        .status-inactive { background-color: #f44336; }

        .row-compact {
            margin-bottom: 0.75rem;
        }

        .form-group-compact {
            margin-bottom: 0.4rem;
        }

        .form-label-compact {
            font-weight: 500;
            font-size: 0.75rem;
            margin-bottom: 0.25rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .avatar-display {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color) 0%, #333333 100%);
            color: var(--secondary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1rem;
            box-shadow: var(--shadow-light);
            margin: 0 auto 0.75rem;
        }

        .credit-summary {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--surface-color) 100%);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: var(--shadow-light);
        }

        .credit-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.8rem;
            padding: 0.25rem 0;
        }

        .credit-item:last-child {
            margin-bottom: 0;
            padding-top: 0.5rem;
            border-top: 1px solid var(--border-color);
            font-weight: 600;
        }



        .grid-row {
            display: flex;
            gap: 0.3rem;
            margin-bottom: 0.3rem;
        }

        .grid-col {
            flex: 1;
        }

        .grid-col-full {
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .scrollable-section::-webkit-scrollbar {
            width: 6px;
        }

        .scrollable-section::-webkit-scrollbar-track {
            background: var(--surface-color);
            border-radius: 3px;
        }

        .scrollable-section::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .scrollable-section::-webkit-scrollbar-thumb:hover {
            background: #bdbdbd;
        }

        /* Compact left column styles */
        .profile-section {
            max-width: 350px;
        }

        .form-row-compact {
            display: flex;
            gap: 0.3rem;
            margin-bottom: 0.4rem;
        }

        .form-col {
            flex: 1;
        }

        .form-col-auto {
            flex: 0 0 auto;
        }

        .input-group-compact {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .badge-status {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-weight: 500;
        }

        .badge-active {
            background-color: #e8f5e8;
            color: #2e7d32;
        }

        .badge-inactive {
            background-color: #ffebee;
            color: #c62828;
        }
    </style>
</head>
<body>
    <!-- Fixed Header -->
    <div class="header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0"><i class="bi bi-person-circle me-2"></i>Customer Profile Management</h4>
                </div>
                <div class="col-md-6 text-end">
                    <span class="badge me-2">ID: <strong id="customer-id">CUST-001</strong></span>
                    <span class="badge">Account: <strong id="account-number">ACC-2024-001</strong></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="full-width-container">
            <!-- Left Column - Profile Section -->
            <div class="profile-column">
                    <div class="profile-card">
                        <div class="section-header">
                            <i class="bi bi-person-fill me-2"></i>Profile
                        </div>

                        <form id="customer-profile-form">
                            <!-- Avatar and Name Section -->
                            <div class="text-center mb-3">
                                <div class="avatar-display" id="avatar-display">BB</div>
                            </div>

                            <div class="form-row-compact">
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="name" value="Bob The Builder" placeholder="Name">
                                        <label for="name">Name</label>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="phone" value="************" placeholder="Phone">
                                        <label for="phone">Phone</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row-compact">
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="email" class="form-control" id="email" value="<EMAIL>" placeholder="Email">
                                        <label for="email">Email</label>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="fax" value="************" placeholder="Fax">
                                        <label for="fax">Fax</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row-compact">
                                <div class="form-col">
                                    <div class="form-floating">
                                        <select class="form-select" id="currency">
                                            <option value="CAD" selected>CAD</option>
                                            <option value="USD">USD</option>
                                        </select>
                                        <label for="currency">Currency</label>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="country" value="Canada" placeholder="Country">
                                        <label for="country">Country</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row-compact">
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="state" value="Ontario" placeholder="State">
                                        <label for="state">State</label>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="region" value="Central" placeholder="Region">
                                        <label for="region">Region</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row-compact">
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="location" value="Toronto" placeholder="Location">
                                        <label for="location">Location</label>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="mobile" value="************" placeholder="Mobile">
                                        <label for="mobile">Mobile</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row-compact">
                                <div class="form-col">
                                    <div class="form-floating">
                                        <select class="form-select" id="type">
                                            <option value="Commercial" selected>Commercial</option>
                                            <option value="Residential">Residential</option>
                                        </select>
                                        <label for="type">Type</label>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="number" class="form-control" id="creditLimit" value="50000" placeholder="Credit Limit">
                                        <label for="creditLimit">Credit Limit</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row-compact">
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="number" class="form-control" id="paymentDueDays" value="30" placeholder="Payment Due Days">
                                        <label for="paymentDueDays">Payment Due Days</label>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="company" value="Construction Co." placeholder="Company">
                                        <label for="company">Company</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row-compact">
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="branch" value="Main Branch" placeholder="Branch">
                                        <label for="branch">Branch</label>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-floating">
                                        <select class="form-select" id="companyType">
                                            <option value="Corporation" selected>Corporation</option>
                                            <option value="Partnership">Partnership</option>
                                            <option value="Sole Proprietorship">Sole Proprietorship</option>
                                        </select>
                                        <label for="companyType">Company Type</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row-compact">
                                <div class="form-col">
                                    <div class="form-floating">
                                        <select class="form-select" id="customerType">
                                            <option value="Premium" selected>Premium</option>
                                            <option value="Standard">Standard</option>
                                        </select>
                                        <label for="customerType">Customer Type</label>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-floating">
                                        <select class="form-select" id="language">
                                            <option value="English" selected>English</option>
                                            <option value="French">French</option>
                                        </select>
                                        <label for="language">Language</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row-compact">
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="number" class="form-control" id="variancePercent" value="5" placeholder="Variance %">
                                        <label for="variancePercent">Variance in %</label>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="number" class="form-control" id="varianceValue" value="2500" placeholder="Variance Value">
                                        <label for="varianceValue">Variance in Value</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row-compact">
                                <div class="form-col">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="paymentTerms" value="Net 30" placeholder="Payment Terms">
                                        <label for="paymentTerms">Payment Terms</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group-compact">
                                <div class="form-floating">
                                    <textarea class="form-control" id="notes" rows="3" style="height: 80px;" placeholder="Notes">Reliable customer with excellent payment history</textarea>
                                    <label for="notes">Notes</label>
                                </div>
                            </div>
                    </form>
                    </div>
                </div>

            <!-- Right Column - Related Data Sections -->
            <div class="data-column">
                    <!-- Credit Details -->
                    <div class="section-header">
                        <i class="bi bi-credit-card me-2"></i>Credit Details
                    </div>
                    <div class="credit-summary">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="credit-item">
                                    <span>Credit Limit in CAD:</span>
                                    <strong>$50,000</strong>
                                </div>
                                <div class="credit-item">
                                    <span>Credit Limit in USD:</span>
                                    <strong>$37,500</strong>
                                </div>
                                <div class="credit-item">
                                    <span>Credit Utilised in CAD:</span>
                                    <strong>$12,500</strong>
                                </div>
                                <div class="credit-item">
                                    <span>Credit Utilised in USD:</span>
                                    <strong>$9,375</strong>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="credit-item">
                                    <span>Credit Available in CAD:</span>
                                    <strong>$37,500</strong>
                                </div>
                                <div class="credit-item">
                                    <span>Credit Available in USD:</span>
                                    <strong>$28,125</strong>
                                </div>
                                <div class="credit-item">
                                    <span>WIP Credit Value in CAD:</span>
                                    <strong>$5,000</strong>
                                </div>
                                <div class="credit-item">
                                    <span>WIP Credit Value in USD:</span>
                                    <strong>$3,750</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- First Row: Addresses and Contacts -->
                    <div class="grid-row">
                        <div class="grid-col">
                            <div class="grid-container">
                                <div class="grid-header">
                                    <div class="grid-header-left">
                                        <span><strong><i class="bi bi-geo-alt-fill me-2"></i>Addresses</strong></span>
                                        <button class="btn-add" title="Add Address"><i class="bi bi-plus"></i></button>
                                    </div>
                                    <div class="grid-header-right">
                                        <input type="text" class="form-control grid-search" placeholder="Search..." id="addresses-search">
                                        <button class="btn-add" title="Advanced Search"><i class="bi bi-plus"></i></button>
                                    </div>
                                </div>
                                <div class="grid-content">
                                    <table class="table table-sm table-striped mb-0" id="addresses-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 20%;">Type</th>
                                                <th style="width: 40%;">Address</th>
                                                <th style="width: 25%;">City</th>
                                                <th style="width: 15%;">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="addresses-tbody">
                                            <!-- Address data will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="grid-pagination">
                                    <span id="addresses-info">1-3 of 3</span>
                                    <div class="pagination-controls">
                                        <button class="btn btn-sm btn-outline-dark" id="addresses-prev">‹</button>
                                        <input type="number" class="pagination-input" id="addresses-page" value="1" min="1">
                                        <span>of 1</span>
                                        <button class="btn btn-sm btn-outline-dark" id="addresses-next">›</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="grid-col">
                            <div class="grid-container">
                                <div class="grid-header">
                                    <div class="grid-header-left">
                                        <span><strong><i class="bi bi-people-fill me-2"></i>Contacts</strong></span>
                                        <button class="btn-add" title="Add Contact"><i class="bi bi-plus"></i></button>
                                    </div>
                                    <div class="grid-header-right">
                                        <input type="text" class="form-control grid-search" placeholder="Search..." id="contacts-search">
                                        <button class="btn-add" title="Advanced Search"><i class="bi bi-plus"></i></button>
                                    </div>
                                </div>
                                <div class="grid-content">
                                    <table class="table table-sm table-striped mb-0" id="contacts-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 25%;">Name</th>
                                                <th style="width: 25%;">Title</th>
                                                <th style="width: 35%;">Email</th>
                                                <th style="width: 15%;">Default</th>
                                            </tr>
                                        </thead>
                                        <tbody id="contacts-tbody">
                                            <!-- Contact data will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="grid-pagination">
                                    <span id="contacts-info">1-3 of 3</span>
                                    <div class="pagination-controls">
                                        <button class="btn btn-sm btn-outline-dark" id="contacts-prev">‹</button>
                                        <input type="number" class="pagination-input" id="contacts-page" value="1" min="1">
                                        <span>of 1</span>
                                        <button class="btn btn-sm btn-outline-dark" id="contacts-next">›</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Second Row: Tax Information and Customer Segments -->
                    <div class="grid-row">
                        <div class="grid-col">
                            <div class="grid-container">
                                <div class="grid-header">
                                    <div class="grid-header-left">
                                        <span><strong><i class="bi bi-calculator me-2"></i>Tax Information</strong></span>
                                        <button class="btn-add" title="Add Tax Info"><i class="bi bi-plus"></i></button>
                                    </div>
                                    <div class="grid-header-right">
                                        <input type="text" class="form-control grid-search" placeholder="Search..." id="taxes-search">
                                        <button class="btn-add" title="Advanced Search"><i class="bi bi-plus"></i></button>
                                    </div>
                                </div>
                                <div class="grid-content">
                                    <table class="table table-sm table-striped mb-0" id="taxes-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 20%;">Code</th>
                                                <th style="width: 45%;">Description</th>
                                                <th style="width: 20%;">Rate %</th>
                                                <th style="width: 15%;">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="taxes-tbody">
                                            <!-- Tax data will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="grid-pagination">
                                    <span id="taxes-info">1-3 of 3</span>
                                    <div class="pagination-controls">
                                        <button class="btn btn-sm btn-outline-dark" id="taxes-prev">‹</button>
                                        <input type="number" class="pagination-input" id="taxes-page" value="1" min="1">
                                        <span>of 1</span>
                                        <button class="btn btn-sm btn-outline-dark" id="taxes-next">›</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="grid-col">
                            <div class="grid-container">
                                <div class="grid-header">
                                    <div class="grid-header-left">
                                        <span><strong><i class="bi bi-tags-fill me-2"></i>Customer Segments</strong></span>
                                        <button class="btn-add" title="Add Segment"><i class="bi bi-plus"></i></button>
                                    </div>
                                    <div class="grid-header-right">
                                        <input type="text" class="form-control grid-search" placeholder="Search..." id="segments-search">
                                        <button class="btn-add" title="Advanced Search"><i class="bi bi-plus"></i></button>
                                    </div>
                                </div>
                                <div class="grid-content">
                                    <table class="table table-sm table-striped mb-0" id="segments-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 25%;">Name</th>
                                                <th style="width: 35%;">Description</th>
                                                <th style="width: 25%;">Benefits</th>
                                                <th style="width: 15%;">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="segments-tbody">
                                            <!-- Segment data will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="grid-pagination">
                                    <span id="segments-info">1-3 of 3</span>
                                    <div class="pagination-controls">
                                        <button class="btn btn-sm btn-outline-dark" id="segments-prev">‹</button>
                                        <input type="number" class="pagination-input" id="segments-page" value="1" min="1">
                                        <span>of 1</span>
                                        <button class="btn btn-sm btn-outline-dark" id="segments-next">›</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Third Row: Service Schedules and Vehicle Fleet -->
                    <div class="grid-row">
                        <div class="grid-col">
                            <div class="grid-container">
                                <div class="grid-header">
                                    <div class="grid-header-left">
                                        <span><strong><i class="bi bi-calendar-event me-2"></i>Service Schedules</strong></span>
                                        <button class="btn-add" title="Add Schedule"><i class="bi bi-plus"></i></button>
                                    </div>
                                    <div class="grid-header-right">
                                        <input type="text" class="form-control grid-search" placeholder="Search..." id="schedules-search">
                                        <button class="btn-add" title="Advanced Search"><i class="bi bi-plus"></i></button>
                                    </div>
                                </div>
                                <div class="grid-content">
                                    <table class="table table-sm table-striped mb-0" id="schedules-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 30%;">Service</th>
                                                <th style="width: 25%;">Type</th>
                                                <th style="width: 30%;">Frequency</th>
                                                <th style="width: 15%;">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="schedules-tbody">
                                            <!-- Schedule data will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="grid-pagination">
                                    <span id="schedules-info">1-3 of 3</span>
                                    <div class="pagination-controls">
                                        <button class="btn btn-sm btn-outline-dark" id="schedules-prev">‹</button>
                                        <input type="number" class="pagination-input" id="schedules-page" value="1" min="1">
                                        <span>of 1</span>
                                        <button class="btn btn-sm btn-outline-dark" id="schedules-next">›</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="grid-col">
                            <div class="grid-container">
                                <div class="grid-header">
                                    <div class="grid-header-left">
                                        <span><strong><i class="bi bi-truck me-2"></i>Vehicle Fleet</strong></span>
                                        <button class="btn-add" title="Add Vehicle"><i class="bi bi-plus"></i></button>
                                    </div>
                                    <div class="grid-header-right">
                                        <input type="text" class="form-control grid-search" placeholder="Search..." id="vehicles-search">
                                        <button class="btn-add" title="Advanced Search"><i class="bi bi-plus"></i></button>
                                    </div>
                                </div>
                                <div class="grid-content">
                                    <table class="table table-sm table-striped mb-0" id="vehicles-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 35%;">Brand/Model</th>
                                                <th style="width: 25%;">Type</th>
                                                <th style="width: 20%;">Year</th>
                                                <th style="width: 20%;">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="vehicles-tbody">
                                            <!-- Vehicle data will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="grid-pagination">
                                    <span id="vehicles-info">1-3 of 3</span>
                                    <div class="pagination-controls">
                                        <button class="btn btn-sm btn-outline-dark" id="vehicles-prev">‹</button>
                                        <input type="number" class="pagination-input" id="vehicles-page" value="1" min="1">
                                        <span>of 1</span>
                                        <button class="btn btn-sm btn-outline-dark" id="vehicles-next">›</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Fourth Row: Contractors and Customer Discounts -->
                    <div class="grid-row">
                        <div class="grid-col">
                            <div class="grid-container">
                                <div class="grid-header">
                                    <div class="grid-header-left">
                                        <span><strong><i class="bi bi-people me-2"></i>Contractors</strong></span>
                                        <button class="btn-add" title="Add Contractor"><i class="bi bi-plus"></i></button>
                                    </div>
                                    <div class="grid-header-right">
                                        <input type="text" class="form-control grid-search" placeholder="Search..." id="contractors-search">
                                        <button class="btn-add" title="Advanced Search"><i class="bi bi-plus"></i></button>
                                    </div>
                                </div>
                                <div class="grid-content">
                                    <table class="table table-sm table-striped mb-0" id="contractors-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 30%;">Name</th>
                                                <th style="width: 35%;">Specialization</th>
                                                <th style="width: 20%;">Rate/Hr</th>
                                                <th style="width: 15%;">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="contractors-tbody">
                                            <!-- Contractor data will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="grid-pagination">
                                    <span id="contractors-info">1-3 of 3</span>
                                    <div class="pagination-controls">
                                        <button class="btn btn-sm btn-outline-dark" id="contractors-prev">‹</button>
                                        <input type="number" class="pagination-input" id="contractors-page" value="1" min="1">
                                        <span>of 1</span>
                                        <button class="btn btn-sm btn-outline-dark" id="contractors-next">›</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="grid-col">
                            <div class="grid-container">
                                <div class="grid-header">
                                    <div class="grid-header-left">
                                        <span><strong><i class="bi bi-percent me-2"></i>Customer Discounts</strong></span>
                                        <button class="btn-add" title="Add Discount"><i class="bi bi-plus"></i></button>
                                    </div>
                                    <div class="grid-header-right">
                                        <input type="text" class="form-control grid-search" placeholder="Search..." id="discounts-search">
                                        <button class="btn-add" title="Advanced Search"><i class="bi bi-plus"></i></button>
                                    </div>
                                </div>
                                <div class="grid-content">
                                    <table class="table table-sm table-striped mb-0" id="discounts-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 30%;">Name</th>
                                                <th style="width: 25%;">Type</th>
                                                <th style="width: 25%;">Parts %</th>
                                                <th style="width: 20%;">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="discounts-tbody">
                                            <!-- Discount data will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="grid-pagination">
                                    <span id="discounts-info">1-3 of 3</span>
                                    <div class="pagination-controls">
                                        <button class="btn btn-sm btn-outline-dark" id="discounts-prev">‹</button>
                                        <input type="number" class="pagination-input" id="discounts-page" value="1" min="1">
                                        <span>of 1</span>
                                        <button class="btn btn-sm btn-outline-dark" id="discounts-next">›</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Rate Contracts - Full Width -->
                    <div class="grid-col-full">
                        <div class="grid-container">
                            <div class="grid-header">
                                <div class="grid-header-left">
                                    <span><strong><i class="bi bi-file-earmark-text me-2"></i>Rate Contracts</strong></span>
                                    <button class="btn-add" title="Add Contract"><i class="bi bi-plus"></i></button>
                                </div>
                                <div class="grid-header-right">
                                    <input type="text" class="form-control grid-search" placeholder="Search contracts..." id="contracts-search">
                                    <button class="btn-add" title="Advanced Search"><i class="bi bi-plus"></i></button>
                                </div>
                            </div>
                            <div class="grid-content">
                                <table class="table table-sm table-striped mb-0" id="contracts-table">
                                    <thead>
                                        <tr>
                                            <th style="width: 25%;">Name</th>
                                            <th style="width: 20%;">Category</th>
                                            <th style="width: 15%;">Type</th>
                                            <th style="width: 15%;">Rate</th>
                                            <th style="width: 10%;">Unit</th>
                                            <th style="width: 15%;">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody id="contracts-tbody">
                                        <!-- Contract data will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                            <div class="grid-pagination">
                                <span id="contracts-info">1-3 of 3</span>
                                <div class="pagination-controls">
                                    <button class="btn btn-sm btn-outline-dark" id="contracts-prev">‹</button>
                                    <input type="number" class="pagination-input" id="contracts-page" value="1" min="1">
                                    <span>of 1</span>
                                    <button class="btn btn-sm btn-outline-dark" id="contracts-next">›</button>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>

    <!-- Fixed Footer -->
    <div class="footer">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-12 text-center">
                    <span class="me-4">
                        <span class="status-indicator status-active"></span>
                        <strong>Active:</strong> <span id="footer-active">Yes</span>
                    </span>
                    <span class="me-4">
                        <span class="status-indicator" id="locked-indicator"></span>
                        <strong>Locked:</strong> <span id="footer-locked">No</span>
                    </span>
                    <span class="me-4">
                        <span class="status-indicator status-active"></span>
                        <strong>Dealer:</strong> <span id="footer-dealer">Yes</span>
                    </span>
                    <span>
                        <span class="status-indicator status-active"></span>
                        <strong>PO Mandatory:</strong> <span id="footer-po">Yes</span>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <script>
        // Customer data from the provided JSON
        const userData = {
            profile: {
                id: "CUST-001",
                customerAccountNumber: "ACC-2024-001",
                firstName: "Bob",
                lastName: "The Builder",
                company: "Construction Co.",
                branch: "Main Branch",
                email: "<EMAIL>",
                phone: "************",
                fax: "************",
                mobile: "************",
                currency: "CAD",
                country: "Canada",
                state: "Ontario",
                region: "Central",
                location: "Toronto",
                type: "Commercial",
                customerType: "Premium",
                language: "English",
                creditLimit: 50000,
                paymentDueDays: 30,
                paymentTerms: "Net 30",
                varianceInValue: 1000,
                varianceInPercent: 5,
                isActive: true,
                isLocked: false,
                isDealer: true,
                isPOMandatory: true,
                customerSince: "2020-01-15",
                notes: "Reliable customer with excellent payment history",
                avatar: "BB"
            },
            creditDetails: {
                creditLimitCAD: 50000,
                creditLimitUSD: 37500,
                creditUtilizedCAD: 12500,
                creditUtilizedUSD: 9375,
                creditAvailableCAD: 37500,
                creditAvailableUSD: 28125,
                wipCreditValueCAD: 5000,
                wipCreditValueUSD: 3750
            },
            contacts: [
                {
                    id: "CONT-001",
                    name: "Bob The Builder",
                    firstName: "Bob",
                    lastName: "The Builder",
                    title: "Owner",
                    dateOfBirth: "1980-05-15",
                    department: "Management",
                    email: "<EMAIL>",
                    phone: "************",
                    mobile: "************",
                    remarks: "Primary contact for all communications",
                    isActive: true,
                    isDefaultContact: true
                },
                {
                    id: "CONT-002",
                    name: "Sarah Johnson",
                    firstName: "Sarah",
                    lastName: "Johnson",
                    title: "Accounts Manager",
                    dateOfBirth: "1985-03-22",
                    department: "Finance",
                    email: "<EMAIL>",
                    phone: "************",
                    mobile: "************",
                    remarks: "Handles all billing and payment inquiries",
                    isActive: true,
                    isDefaultContact: false
                },
                {
                    id: "CONT-003",
                    name: "Mike Wilson",
                    firstName: "Mike",
                    lastName: "Wilson",
                    title: "Project Manager",
                    dateOfBirth: "1978-11-10",
                    department: "Operations",
                    email: "<EMAIL>",
                    phone: "************",
                    mobile: "************",
                    remarks: "Technical contact for project coordination",
                    isActive: true,
                    isDefaultContact: false
                }
            ],
            addresses: [
                {
                    id: "ADDR-001",
                    type: "Billing",
                    street1: "123 Main Street",
                    street2: "Suite 100",
                    city: "Springfield",
                    state: "IL",
                    zipCode: "62701",
                    country: "USA",
                    district: "Downtown",
                    isDefault: true,
                    isActive: true
                },
                {
                    id: "ADDR-002",
                    type: "Shipping",
                    street1: "456 Oak Avenue",
                    street2: "",
                    city: "Springfield",
                    state: "IL",
                    zipCode: "62702",
                    country: "USA",
                    district: "Industrial",
                    isDefault: false,
                    isActive: true
                },
                {
                    id: "ADDR-003",
                    type: "Job Site",
                    street1: "789 Construction Blvd",
                    street2: "Building A",
                    city: "Springfield",
                    state: "IL",
                    zipCode: "62703",
                    country: "USA",
                    district: "Commercial",
                    isDefault: false,
                    isActive: true
                }
            ],
            taxes: [
                {
                    id: "TAX-001",
                    code: "HST",
                    description: "Harmonized Sales Tax",
                    rate: 13.0,
                    isActive: true
                },
                {
                    id: "TAX-002",
                    code: "GST",
                    description: "Goods and Services Tax",
                    rate: 5.0,
                    isActive: true
                },
                {
                    id: "TAX-003",
                    code: "PST",
                    description: "Provincial Sales Tax",
                    rate: 8.0,
                    isActive: false
                }
            ],
            taxStructures: [
                {
                    id: "TAXSTR-001",
                    name: "Standard Tax Structure",
                    description: "Default tax structure for Canadian customers",
                    taxCodes: ["HST", "GST"],
                    totalRate: 13.0,
                    isActive: true
                },
                {
                    id: "TAXSTR-002",
                    name: "Export Tax Structure",
                    description: "Tax structure for export customers",
                    taxCodes: [],
                    totalRate: 0.0,
                    isActive: true
                }
            ],
            segments: {
                primarySegment: "Premium Customer",
                secondarySegment: "Construction Industry",
                segmentDetails: [
                    {
                        id: "SEG-001",
                        name: "Premium Customer",
                        description: "High-value customers with excellent payment history",
                        criteria: "Annual revenue > $100K",
                        benefits: "Priority support, extended payment terms",
                        isActive: true,
                        isPrimary: true
                    },
                    {
                        id: "SEG-002",
                        name: "Construction Industry",
                        description: "Customers in construction and building trades",
                        criteria: "Industry classification",
                        benefits: "Industry-specific pricing, bulk discounts",
                        isActive: true,
                        isPrimary: false
                    },
                    {
                        id: "SEG-003",
                        name: "Commercial Client",
                        description: "Business-to-business commercial customers",
                        criteria: "Business registration and commercial operations",
                        benefits: "Volume discounts, flexible payment terms",
                        isActive: true,
                        isPrimary: false
                    }
                ]
            },
            serviceSchedules: [
                {
                    id: "SCHED-001",
                    serviceName: "Weekly Delivery",
                    serviceType: "Delivery",
                    serviceDate: "2024-01-15",
                    workOrderNumber: "WO-2024-001",
                    frequency: "Weekly",
                    dayOfWeek: "Monday",
                    startTime: "08:00",
                    endTime: "17:00",
                    startDate: "2024-01-01",
                    endDate: "2024-12-31",
                    status: "Active",
                    closureReason: "",
                    notes: "Standard delivery window"
                },
                {
                    id: "SCHED-002",
                    serviceName: "Monthly Maintenance",
                    serviceType: "Maintenance",
                    serviceDate: "2024-01-15",
                    workOrderNumber: "WO-2024-002",
                    frequency: "Monthly",
                    dayOfMonth: 15,
                    startTime: "09:00",
                    endTime: "12:00",
                    startDate: "2024-01-01",
                    endDate: "2024-12-31",
                    status: "Completed",
                    closureReason: "Routine maintenance completed successfully",
                    notes: "Equipment maintenance check"
                },
                {
                    id: "SCHED-003",
                    serviceName: "Emergency Repair",
                    serviceType: "Repair",
                    serviceDate: "2024-01-20",
                    workOrderNumber: "WO-2024-003",
                    frequency: "As Needed",
                    dayOfWeek: "",
                    startTime: "24/7",
                    endTime: "24/7",
                    startDate: "2024-01-01",
                    endDate: "2024-12-31",
                    status: "Pending",
                    closureReason: "",
                    notes: "Emergency repair services available 24/7"
                }
            ],
            vehicles: [
                {
                    id: "VEH-001",
                    brand: "Ford",
                    model: "F-350",
                    vehicleType: "Delivery Truck",
                    vinNumber: "1FDRF3G69NEA12345",
                    year: 2022,
                    licensePlate: "ABC-123",
                    capacity: "3 tons",
                    status: "Active",
                    assignedDriver: "John Smith",
                    lastMaintenance: "2024-01-15",
                    nextMaintenance: "2024-04-15"
                },
                {
                    id: "VEH-002",
                    brand: "Caterpillar",
                    model: "320D",
                    vehicleType: "Crane",
                    vinNumber: "CAT320D2021ABC123",
                    year: 2021,
                    licensePlate: "XYZ-789",
                    capacity: "20 tons",
                    status: "Active",
                    assignedOperator: "Mike Johnson",
                    lastMaintenance: "2024-02-01",
                    nextMaintenance: "2024-05-01"
                },
                {
                    id: "VEH-003",
                    brand: "Mercedes-Benz",
                    model: "Sprinter 2500",
                    vehicleType: "Van",
                    vinNumber: "WD3PE8CC5DP123456",
                    year: 2023,
                    licensePlate: "DEF-456",
                    capacity: "2 tons",
                    status: "Active",
                    assignedDriver: "Sarah Wilson",
                    lastMaintenance: "2024-01-10",
                    nextMaintenance: "2024-04-10"
                }
            ],
            contractors: [
                {
                    id: "CONTR-001",
                    name: "Elite Construction Services",
                    contactPerson: "David Brown",
                    email: "<EMAIL>",
                    phone: "************",
                    specialization: "Electrical",
                    agreementNumber: "AGR-2023-001",
                    fromDate: "2023-01-01",
                    toDate: "2024-12-31",
                    contractValue: 150000,
                    unit: "CAD",
                    currency: "CAD",
                    rating: 4.8,
                    status: "Active",
                    hourlyRate: 85.00,
                    remarks: "Excellent electrical contractor with 15+ years experience"
                },
                {
                    id: "CONTR-002",
                    name: "Pro Plumbing Solutions",
                    contactPerson: "Lisa Garcia",
                    email: "<EMAIL>",
                    phone: "************",
                    specialization: "Plumbing",
                    agreementNumber: "AGR-2023-002",
                    fromDate: "2023-06-01",
                    toDate: "2024-12-31",
                    contractValue: 120000,
                    unit: "CAD",
                    currency: "CAD",
                    rating: 4.6,
                    status: "Active",
                    hourlyRate: 75.00,
                    remarks: "Reliable plumbing services for commercial projects"
                },
                {
                    id: "CONTR-003",
                    name: "Advanced HVAC Systems",
                    contactPerson: "Robert Chen",
                    email: "<EMAIL>",
                    phone: "************",
                    specialization: "HVAC",
                    agreementNumber: "AGR-2023-003",
                    fromDate: "2023-03-01",
                    toDate: "2025-02-28",
                    contractValue: 200000,
                    unit: "CAD",
                    currency: "CAD",
                    rating: 4.9,
                    status: "Active",
                    hourlyRate: 95.00,
                    remarks: "Premium HVAC contractor specializing in commercial systems"
                }
            ],
            discounts: [
                {
                    id: "DISC-001",
                    name: "Volume Discount",
                    type: "Percentage",
                    partDiscount: 10,
                    serviceDiscount: 8,
                    effectiveDate: "2024-01-01",
                    minimumAmount: 10000,
                    maximumAmount: null,
                    startDate: "2024-01-01",
                    endDate: "2024-12-31",
                    isActive: true,
                    description: "Volume discount for parts and services"
                },
                {
                    id: "DISC-002",
                    name: "Early Payment Discount",
                    type: "Percentage",
                    partDiscount: 2,
                    serviceDiscount: 2,
                    effectiveDate: "2024-01-01",
                    minimumAmount: 1000,
                    maximumAmount: null,
                    startDate: "2024-01-01",
                    endDate: "2024-12-31",
                    isActive: true,
                    description: "Early payment discount for parts and services"
                },
                {
                    id: "DISC-003",
                    name: "Seasonal Discount",
                    type: "Percentage",
                    partDiscount: 15,
                    serviceDiscount: 12,
                    effectiveDate: "2024-06-01",
                    minimumAmount: 5000,
                    maximumAmount: 50000,
                    startDate: "2024-06-01",
                    endDate: "2024-08-31",
                    isActive: true,
                    description: "Summer seasonal discount promotion"
                }
            ],
            rateContracts: [
                {
                    id: "RATE-001",
                    name: "Standard Construction Materials",
                    type: "Fixed Rate",
                    category: "Materials",
                    rate: 125.00,
                    unit: "per hour",
                    effectiveFrom: "2024-01-01",
                    effectiveTo: "2024-12-31",
                    uploadedBy: "John Admin",
                    uploadedDate: "2023-12-15",
                    isActive: true,
                    terms: "Standard hourly rate for construction materials delivery",
                    minimumCharge: 250.00
                },
                {
                    id: "RATE-002",
                    name: "Equipment Rental",
                    type: "Tiered Rate",
                    category: "Equipment",
                    rate: 200.00,
                    unit: "per day",
                    effectiveFrom: "2024-01-01",
                    effectiveTo: "2024-12-31",
                    uploadedBy: "Sarah Manager",
                    uploadedDate: "2023-12-20",
                    isActive: true,
                    terms: "Daily equipment rental rate with weekly discounts",
                    minimumCharge: 200.00
                },
                {
                    id: "RATE-003",
                    name: "Premium Service Contract",
                    type: "Variable Rate",
                    category: "Services",
                    rate: 150.00,
                    unit: "per hour",
                    effectiveFrom: "2024-02-01",
                    effectiveTo: "2025-01-31",
                    uploadedBy: "Mike Supervisor",
                    uploadedDate: "2024-01-25",
                    isActive: true,
                    terms: "Premium service rate for specialized technical work",
                    minimumCharge: 300.00
                }
            ]
        };

        // Initialize the form when document is ready
        $(document).ready(function() {
            populateProfileForm();
            populateGrids();
            setupEventHandlers();
            updateFooterStatus();
        });

        // Populate profile form with data
        function populateProfileForm() {
            const profile = userData.profile;

            // Update header information
            $('#customer-id').text(profile.id);
            $('#account-number').text(profile.customerAccountNumber);

            // Update avatar
            $('#avatar-display').text(profile.avatar);

            // Populate form fields
            Object.keys(profile).forEach(key => {
                const element = $('#' + key);
                if (element.length) {
                    if (element.is('select')) {
                        element.val(profile[key]);
                    } else if (element.is('textarea')) {
                        element.val(profile[key]);
                    } else {
                        element.val(profile[key]);
                    }
                }
            });
        }

        // Populate all grid sections
        function populateGrids() {
            populateAddressesGrid();
            populateContactsGrid();
            populateTaxesGrid();
            populateSegmentsGrid();
            populateSchedulesGrid();
            populateVehiclesGrid();
            populateContractorsGrid();
            populateDiscountsGrid();
            populateContractsGrid();
        }

        // Populate addresses grid
        function populateAddressesGrid() {
            const tbody = $('#addresses-tbody');
            tbody.empty();

            userData.addresses.forEach(address => {
                const fullAddress = `${address.street1}${address.street2 ? ', ' + address.street2 : ''}`;
                const statusClass = address.isActive ? 'status-active' : 'status-inactive';
                const row = `
                    <tr>
                        <td><strong>${address.type}</strong></td>
                        <td>${fullAddress}</td>
                        <td>${address.city}</td>
                        <td>${address.state}</td>
                        <td><span class="status-indicator ${statusClass}"></span>${address.isActive ? 'Active' : 'Inactive'}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // Populate contacts grid
        function populateContactsGrid() {
            const tbody = $('#contacts-tbody');
            tbody.empty();

            userData.contacts.forEach(contact => {
                const statusClass = contact.isActive ? 'status-active' : 'status-inactive';
                const defaultIcon = contact.isDefaultContact ? '★' : '';
                const row = `
                    <tr>
                        <td><strong>${contact.name}</strong></td>
                        <td>${contact.title}</td>
                        <td>${contact.department}</td>
                        <td>${contact.email}</td>
                        <td>${contact.phone}</td>
                        <td>${defaultIcon}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // Populate taxes grid
        function populateTaxesGrid() {
            const tbody = $('#taxes-tbody');
            tbody.empty();

            userData.taxes.forEach(tax => {
                const statusClass = tax.isActive ? 'status-active' : 'status-inactive';
                const row = `
                    <tr>
                        <td><strong>${tax.code}</strong></td>
                        <td>${tax.description}</td>
                        <td>${tax.rate}%</td>
                        <td><span class="status-indicator ${statusClass}"></span>${tax.isActive ? 'Active' : 'Inactive'}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // Populate segments grid
        function populateSegmentsGrid() {
            const tbody = $('#segments-tbody');
            tbody.empty();

            userData.segments.segmentDetails.forEach(segment => {
                const statusClass = segment.isActive ? 'status-active' : 'status-inactive';
                const primaryIcon = segment.isPrimary ? '★' : '';
                const row = `
                    <tr>
                        <td><strong>${segment.name}</strong></td>
                        <td>${segment.description}</td>
                        <td>${segment.benefits}</td>
                        <td>${primaryIcon}</td>
                        <td><span class="status-indicator ${statusClass}"></span>${segment.isActive ? 'Active' : 'Inactive'}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // Populate schedules grid
        function populateSchedulesGrid() {
            const tbody = $('#schedules-tbody');
            tbody.empty();

            userData.serviceSchedules.forEach(schedule => {
                const statusClass = schedule.status === 'Active' ? 'status-active' :
                                  schedule.status === 'Completed' ? 'status-active' : 'status-inactive';
                const row = `
                    <tr>
                        <td><strong>${schedule.serviceName}</strong></td>
                        <td>${schedule.serviceType}</td>
                        <td>${schedule.frequency}</td>
                        <td>${schedule.workOrderNumber}</td>
                        <td><span class="status-indicator ${statusClass}"></span>${schedule.status}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // Populate vehicles grid
        function populateVehiclesGrid() {
            const tbody = $('#vehicles-tbody');
            tbody.empty();

            userData.vehicles.forEach(vehicle => {
                const statusClass = vehicle.status === 'Active' ? 'status-active' : 'status-inactive';
                const row = `
                    <tr>
                        <td><strong>${vehicle.brand} ${vehicle.model}</strong></td>
                        <td>${vehicle.vehicleType}</td>
                        <td>${vehicle.year}</td>
                        <td>${vehicle.licensePlate}</td>
                        <td>${vehicle.capacity}</td>
                        <td><span class="status-indicator ${statusClass}"></span>${vehicle.status}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // Populate contractors grid
        function populateContractorsGrid() {
            const tbody = $('#contractors-tbody');
            tbody.empty();

            userData.contractors.forEach(contractor => {
                const statusClass = contractor.status === 'Active' ? 'status-active' : 'status-inactive';
                const row = `
                    <tr>
                        <td><strong>${contractor.name}</strong></td>
                        <td>${contractor.specialization}</td>
                        <td>${contractor.contactPerson}</td>
                        <td>$${contractor.hourlyRate}</td>
                        <td>${contractor.rating}/5</td>
                        <td><span class="status-indicator ${statusClass}"></span>${contractor.status}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // Populate discounts grid
        function populateDiscountsGrid() {
            const tbody = $('#discounts-tbody');
            tbody.empty();

            userData.discounts.forEach(discount => {
                const statusClass = discount.isActive ? 'status-active' : 'status-inactive';
                const minAmount = discount.minimumAmount ? `$${discount.minimumAmount.toLocaleString()}` : 'N/A';
                const row = `
                    <tr>
                        <td><strong>${discount.name}</strong></td>
                        <td>${discount.type}</td>
                        <td>${discount.partDiscount}%</td>
                        <td>${discount.serviceDiscount}%</td>
                        <td>${minAmount}</td>
                        <td><span class="status-indicator ${statusClass}"></span>${discount.isActive ? 'Active' : 'Inactive'}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // Populate contracts grid
        function populateContractsGrid() {
            const tbody = $('#contracts-tbody');
            tbody.empty();

            userData.rateContracts.forEach(contract => {
                const statusClass = contract.isActive ? 'status-active' : 'status-inactive';
                const row = `
                    <tr>
                        <td><strong>${contract.name}</strong></td>
                        <td>${contract.category}</td>
                        <td>${contract.type}</td>
                        <td>$${contract.rate}</td>
                        <td>${contract.unit}</td>
                        <td><span class="status-indicator ${statusClass}"></span>${contract.isActive ? 'Active' : 'Inactive'}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // Setup event handlers for search and pagination
        function setupEventHandlers() {
            // Search functionality for all grids
            $('.grid-search').on('input', function() {
                const searchTerm = $(this).val().toLowerCase();
                const gridId = $(this).attr('id').replace('-search', '');
                const table = $('#' + gridId.replace('s', '') + 's-table');

                table.find('tbody tr').each(function() {
                    const rowText = $(this).text().toLowerCase();
                    $(this).toggle(rowText.includes(searchTerm));
                });
            });

            // Pagination buttons (simplified - just for demonstration)
            $('.btn-outline-dark').on('click', function() {
                // Pagination logic would go here
                console.log('Pagination clicked:', $(this).attr('id'));
            });
        }

        // Update footer status indicators
        function updateFooterStatus() {
            const profile = userData.profile;

            $('#footer-active').text(profile.isActive ? 'Yes' : 'No');
            $('#footer-locked').text(profile.isLocked ? 'Yes' : 'No');
            $('#footer-dealer').text(profile.isDealer ? 'Yes' : 'No');
            $('#footer-po').text(profile.isPOMandatory ? 'Yes' : 'No');

            // Update status indicators
            $('#locked-indicator').addClass(profile.isLocked ? 'status-inactive' : 'status-active');
        }

        // Format currency values
        function formatCurrency(amount, currency = 'CAD') {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: currency
            }).format(amount);
        }

        // Handle form changes
        $('#customer-profile-form').on('change', 'input, select, textarea', function() {
            const fieldId = $(this).attr('id');
            const value = $(this).val();

            // Update the userData object
            if (userData.profile.hasOwnProperty(fieldId)) {
                userData.profile[fieldId] = value;

                // Update avatar if name changes
                if (fieldId === 'firstName' || fieldId === 'lastName') {
                    const firstName = $('#firstName').val();
                    const lastName = $('#lastName').val();
                    const avatar = (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
                    $('#avatar-display').text(avatar);
                    userData.profile.avatar = avatar;
                }
            }

            console.log('Field updated:', fieldId, value);
        });
    </script>
</body>
</html>
